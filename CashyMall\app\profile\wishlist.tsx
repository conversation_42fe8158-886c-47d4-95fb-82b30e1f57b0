import React, { useEffect, useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  useColorScheme,
  Image,
  FlatList,
  Platform,
  Alert
} from 'react-native';
import { useRouter } from 'expo-router';
import { Ionicons } from '@expo/vector-icons';
import Header from '../../components/Header';
import Colors from '../../constants/Colors';
import Layout from '../../constants/Layout';
import { addToCart } from '../../services/api/cart';
import { addToWishlist, checkWishlist, getWishlists, removeFromWishlist } from '@/services/api/wishlist';
import { Wishlist, WishlistResponse } from '@/types/product';
import ProductCard from '@/components/ProductCard';

// Mock wishlist data
const wishlistItems = [
  {
    id: '1',
    name: 'Wireless Headphones',
    price: 129.99,
    discountPrice: 99.99,
    imageUrl: 'https://images.unsplash.com/photo-1505740420928-5e560c06d30e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    inStock: true
  },
  {
    id: '4',
    name: 'Stylish Sunglasses',
    price: 79.99,
    imageUrl: 'https://images.unsplash.com/photo-1572635196237-14b3f281503f?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1160&q=80',
    inStock: true
  },
  {
    id: '7',
    name: 'Smartphone',
    price: 899.99,
    discountPrice: 799.99,
    imageUrl: 'https://images.unsplash.com/photo-1598327105666-5b89351aff97?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1227&q=80',
    inStock: false
  },
  {
    id: '10',
    name: 'Running Shoes',
    price: 129.99,
    discountPrice: 89.99,
    imageUrl: 'https://images.unsplash.com/photo-1542291026-7eec264c27ff?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1170&q=80',
    inStock: true
  }
];

export default function WishlistScreen() {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
  const router = useRouter();

  const [wishlistItems, setWishlistItems] = useState<Wishlist[]>([]);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchWishlist = async () => {
      try {
        const response = await getWishlists() ;
        console.log('Wishlist Response:', response.data.products);
        if (response && response.data) {
          setWishlistItems( response.data.products);
          console.log('Wishlist Items:', response.data.products);
        }
      } catch (error) {
        console.error('Failed to load wishlist:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchWishlist();
  }, []);
  const handleAddToWishlist = async (productId :string) => {
    try {
      const checkItem = await checkWishlist(productId);
      console.log('checkoo',checkItem)
      if (!checkItem.data){

      
      const response = await addToWishlist(productId);
      if (response.error) {
           Alert.alert('Error', 'Failed to add item to wishlist');
        return;
      }
      Alert.alert('Success', 'Item added to wishlist');}
      else {
 const response = await removeFromWishlist(productId);
      if (response.error) {
           Alert.alert('Error', 'Failed to remove item from wishlist');
        return;
      }
       setWishlistItems(prevItems =>
        prevItems.filter(item => item.id !== productId)
      );
      Alert.alert('Success', 'Item removeed from wishlist');
       
      }
    }
    catch(err) {
 console.error('Error adding to wishlist:', err);
      Alert.alert('Error', 'Failed to add item to wishlist');
    }
  } 
  const handleAddToCart = async (id:string) => {
    try {
      const response = await addToCart(id);
      if (response.error) {
        Alert.alert('Error', 'Failed to add item to cart');
        return;
      }
      Alert.alert('Success', 'Item added to cart');
    } catch (err) {
      console.error('Error adding to cart:', err);
      Alert.alert('Error', 'Failed to add item to cart');
    }
  };

  const handleRemoveFromWishlist = (itemId: string) => {
    Alert.alert(
      'Remove from Wishlist',
      'Are you sure you want to remove this item from your wishlist?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: () => {
            // Ideally call backend delete API here too
            setWishlistItems(prev => prev.filter(item => item.id !== itemId));
          },
        },
      ]
    );
  };

  const renderWishlistItem = ({ item }: { item: Wishlist }) => (
    <View style={[styles.itemCard, { backgroundColor: colors.cardBackground }]}>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => handleRemoveFromWishlist(item.id)}
      >
        <Ionicons name="close" size={18} color={colors.text} />
      </TouchableOpacity>

      <TouchableOpacity
        style={styles.itemContent}
        onPress={() => router.push(`/product/${item.id}`)}
      >
        <Image source={{ uri: item.imageUrl }} style={styles.itemImage} resizeMode="cover" />

        <View style={styles.itemInfo}>
          <Text style={[styles.itemName, { color: colors.text }]} numberOfLines={2}>
            {item.name}
          </Text>

          <View style={styles.priceContainer}>
            {item.discountPrice ? (
              <>
                <Text style={[styles.discountPrice, { color: colors.primary }]}>
                  ${item.discountPrice.toFixed(2)}
                </Text>
                <Text style={[styles.originalPrice, { color: colors.tabIconDefault }]}>
                  ${item.price.toFixed(2)}
                </Text>
              </>
            ) : (
              <Text style={[styles.price, { color: colors.text }]}>
                ${item.price.toFixed(2)}
              </Text>
            )}
          </View>
        </View>
      </TouchableOpacity>

      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.addToCartButton, { backgroundColor: colors.primary }]}
          onPress={() => handleAddToCart(item.id)}
        >
          <Ionicons name="cart-outline" size={16} color="#fff" />
          <Text style={styles.addToCartText}>Add to Cart</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={[styles.container, { backgroundColor: colors.background }]}>
        <Text style={{ color: colors.text }}>Loading...</Text>
      </View>
    );
  }

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => router.back()}>
          <Ionicons name="chevron-back" size={24} color={colors.text} />
        </TouchableOpacity>
        <Text style={[styles.headerTitle, { color: colors.text }]}>Wishlist</Text>
        <View style={styles.placeholder} />
      </View>

      {wishlistItems.length > 0 ? (
        <FlatList
          data={wishlistItems}
          numColumns={2}
         renderItem={({ item }) => (
                   <ProductCard
                     id={item.id.toString()}
                     name={item.name}
                     price={item.price}
                     discountPrice={item.discountPrice || undefined}
                     imageUrl={item.imageUrl}
                    //  isNew={item.tags?.some(tag => tag.name === 'New Arrival')}
                     onAddToCart={() => handleAddToCart(item.id)}
                                   onAddToWishlist={() => handleAddToWishlist(item.id.toString())}

                   />
                 )}
                 keyExtractor={item => item.id.toString()}
          contentContainerStyle={styles.wishlistContainer}
          columnWrapperStyle={styles.productRow}
          showsVerticalScrollIndicator={true}
        />
      ) : (
        <View style={styles.emptyContainer}>
          <Ionicons name="heart-outline" size={64} color={colors.tabIconDefault} />
          <Text style={[styles.emptyTitle, { color: colors.text }]}>Your Wishlist is Empty</Text>
          <Text style={[styles.emptySubtitle, { color: colors.tabIconDefault }]}>
            Save items you like by tapping the heart icon on products
          </Text>
          <TouchableOpacity
            style={[styles.shopButton, { backgroundColor: colors.primary }]}
            onPress={() => router.push('/(tabs)/shop')}
          >
            <Text style={styles.shopButtonText}>Start Shopping</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
}
const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Layout.spacing.md,
    paddingTop: Layout.spacing.lg,
    paddingBottom: Layout.spacing.md,
  },
  backButton: {
    padding: Layout.spacing.xs,
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  placeholder: {
    width: 32,
  },
  wishlistContainer: {
    padding: Layout.spacing.md,
  },
   productRow: {
    justifyContent: 'space-between',
  },
  itemCard: {
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.md,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
  },
  removeButton: {
    position: 'absolute',
    top: Layout.spacing.xs,
    right: Layout.spacing.xs,
    zIndex: 1,
    width: 24,
    height: 24,
    borderRadius: 12,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  itemContent: {
    flexDirection: 'row',
    padding: Layout.spacing.md,
  },
  itemImage: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.sm,
  },
  itemInfo: {
    flex: 1,
    marginLeft: Layout.spacing.md,
    justifyContent: 'center',
  },
  itemName: {
    fontSize: 16,
    fontWeight: '500',
    marginBottom: Layout.spacing.xs,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
  },
  discountPrice: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: Layout.spacing.xs,
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
  outOfStock: {
    fontSize: 14,
    color: '#ef4444',
    fontWeight: '500',
  },
  actionButtons: {
    padding: Layout.spacing.md,
    paddingTop: 0,
  },
  addToCartButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
  },
  addToCartText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
    marginLeft: 6,
  },
  emptyContainer: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    padding: Layout.spacing.lg,
  },
  emptyTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: Layout.spacing.md,
    marginBottom: Layout.spacing.xs,
  },
  emptySubtitle: {
    fontSize: 14,
    textAlign: 'center',
    marginBottom: Layout.spacing.lg,
  },
  shopButton: {
    paddingHorizontal: Layout.spacing.lg,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.md,
  },
  shopButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '600',
  },
});
