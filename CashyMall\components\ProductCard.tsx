import { Ionicons } from '@expo/vector-icons';
import { Link } from 'expo-router';
import React, { useEffect, useState } from 'react';
import {
    Image,
    Platform,
    StyleSheet,
    Text,
    TouchableOpacity,
    useColorScheme,
    View
} from 'react-native';
import Colors from '../constants/Colors';
import Layout from '../constants/Layout';
import { checkWishlist } from '@/services/api/wishlist';

interface ProductCardProps {
  id: string;
  name: string;
  price: number;
  discountPrice?: number;
  imageUrl: string;
  isNew?: boolean;
  onAddToCart?: () => void;
  onAddToWishlist?: () => void;
}

export default function ProductCard({
  id,
  name,
  price,
  discountPrice,
  imageUrl,
  isNew = false,
  onAddToCart,
  onAddToWishlist,
}: ProductCardProps) {
  const colorScheme = useColorScheme() || 'light';
  const colors = Colors[colorScheme];
const [isInWishlist, setIsInWishlist] = useState<boolean>(false);

useEffect(() => {
  const checkIfInWishlist = async () => {
    try {
      const result = await checkWishlist(id); // assumes productId is in scope
      setIsInWishlist(!!result.data); // set true if in wishlist
    } catch (err) {
      console.error("Failed to check wishlist status", err);
    }
  };

  checkIfInWishlist();
}, [id]);
  // Ensure price is a number and handle null/undefined values
  const safePrice = typeof price === 'number' ? price : 0;
  const safeDiscountPrice = typeof discountPrice === 'number' ? discountPrice : null;

  // Check if there's a valid discount
  const hasDiscount = safeDiscountPrice !== null && safeDiscountPrice < safePrice;

  // Create style objects with dynamic properties to avoid array styles for web
  const containerStyle = {
    ...styles.container,
    backgroundColor: colors.cardBackground
  };

  const badgeStyle = {
    ...styles.badge,
    backgroundColor: colors.primary
  };

   const discountStyle = {
    ...styles.badgeDiscount,
    backgroundColor: colors.primary
  };
  const cartButtonStyle = {
    ...styles.cartButton,
    backgroundColor: colors.primary
  };

  const cartButtonWishStyle = {
    ...styles.cartButtonwish,
    backgroundColor: colors.primary
  }

  const nameStyle = {
    ...styles.name,
    color: colors.text
  };

  const discountPriceStyle = {
    ...styles.discountPrice,
    color: colors.primary
  };

  const originalPriceStyle = {
    ...styles.originalPrice,
    color: colors.tabIconDefault
  };

  const priceStyle = {
    ...styles.price,
    color: colors.text
  };

  return (

      <TouchableOpacity style={containerStyle}>
        <View style={styles.imageContainer}>
          <Link href={`/product/${id}`} asChild>
          <Image
            source={{ uri: imageUrl }}
            style={styles.image}
            resizeMode="cover"
          /></Link>
          {isNew && (
            <View style={badgeStyle}>
              <Text style={styles.badgeText}>NEW</Text>
            </View>
          )}
           {hasDiscount && (
            <View style={discountStyle}>
              <Text style={styles.badgeDiscountText}>${safeDiscountPrice}</Text>
            </View>
          )}
          <TouchableOpacity
            style={cartButtonStyle}
            onPress={(e) => {
              e.stopPropagation();
              onAddToCart && onAddToCart();
            }}
          >
            <Ionicons name="cart-outline" size={18} color="#ffffff" />
          </TouchableOpacity>
          <TouchableOpacity
            style={cartButtonWishStyle}
            onPress={(e) => {
              e.stopPropagation();
              onAddToWishlist && onAddToWishlist();
            }}
          >
              <Ionicons
    name={isInWishlist ? "heart" : "heart-outline"}
    size={18}
    color={isInWishlist ? "red" : "#ffffff"}
  />
          </TouchableOpacity>
        </View>
<Link href={`/product/${id}`} asChild>
        <View style={styles.infoContainer}>
          <Text
            style={nameStyle}
            numberOfLines={2}
          >
            {name}
          </Text>

          <View style={styles.priceContainer}>
            {hasDiscount ? (
              <>
                <Text style={discountPriceStyle}>
                  ${(safePrice-( safePrice * safeDiscountPrice /100)).toFixed(2)}
                </Text>
                <Text style={originalPriceStyle}>
                  ${safePrice.toFixed(2)}
                </Text>
              </>
            ) : (
              <Text style={priceStyle}>
                ${safePrice.toFixed(2)}
              </Text>
            )}
          </View>
        </View></Link>
      </TouchableOpacity>

  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: Layout.borderRadius.md,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 2,
      },
      web: {
        boxShadow: '0 2px 4px rgba(0, 0, 0, 0.1)',
      }
    }),
    width: (Layout.window.width - (Layout.spacing.md * 3)) / 2, // 2 columns with spacing
    marginBottom: Layout.spacing.md,
  },
  imageContainer: {
    position: 'relative',
    height: 180,
  },
  image: {
    width: '100%',
    height: '100%',
  },
  badge: {
    position: 'absolute',
    top: Layout.spacing.xs,
    left: Layout.spacing.xs,
    paddingHorizontal: Layout.spacing.xs,
    paddingVertical: 2,
    borderRadius: Layout.borderRadius.sm,
  },
  badgeText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },

   badgeDiscount: {
    position: 'absolute',
    top: Layout.spacing.xs,
    right: Layout.spacing.xs,
    paddingHorizontal: Layout.spacing.xs,
    paddingVertical: 2,
    borderRadius: Layout.borderRadius.sm,
  },
  badgeDiscountText: {
    color: '#ffffff',
    fontSize: 10,
    fontWeight: 'bold',
  },
  cartButton: {
    position: 'absolute',
    bottom: Layout.spacing.xs,
    right: Layout.spacing.xs,
    width: 32,
    height: 32,
    borderRadius: Layout.borderRadius.round,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cartButtonwish: {
        position: 'absolute',
    bottom: Layout.spacing.xs,
    left: Layout.spacing.xs,
    width: 32,
    height: 32,
    borderRadius: Layout.borderRadius.round,
    alignItems: 'center',
    justifyContent: 'center',
  },
  infoContainer: {
    padding: Layout.spacing.sm,
  },
  name: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: Layout.spacing.xs,
  },
  priceContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  price: {
    fontSize: 16,
    fontWeight: '600',
  },
  discountPrice: {
    fontSize: 16,
    fontWeight: '600',
    marginRight: Layout.spacing.xs,
  },
  originalPrice: {
    fontSize: 14,
    textDecorationLine: 'line-through',
  },
});
